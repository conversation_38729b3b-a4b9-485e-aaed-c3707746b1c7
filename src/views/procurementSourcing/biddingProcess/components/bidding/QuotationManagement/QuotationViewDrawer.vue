<template>
  <yun-drawer
      v-model="visible"
      :title="`在线报价`"
      size="X-large"
      destroy-on-close
      confirm-button-text="提交"
      cancel-button-text="取消"
      @confirm="handleClose"
  >
    <div class="drawer-content">
     <div class="flex justify-between items-center mb-6">
       <!-- 筛选和操作按钮区域 -->
       <el-form class="filter-form">
         <el-form-item label="物料名称" label-width="120px">
           <el-input
               v-model="materialNameFilter"
               placeholder="请输入物料名称"
               style="width: 200px"
               clearable
               @input="handleFilterChange"
           />
         </el-form-item>
       </el-form>

       <div>
         <el-button type="danger" border @click="handleClearQuotation">清空报价</el-button>
         <el-button border @click="handleExportQuotation">导出报价清单</el-button>
         <el-button border @click="handleUploadQuotation">上传报价清单</el-button>
       </div>
     </div>
      <el-table
          :data="filteredHistoryData"
          style="width: 100%"
          border
          stripe
      >
        <el-table-column
            label="序号"
            type="index"
            width="60"
            align="center"
        />
        <el-table-column
            label="物料编码"
            prop="materialCode"
            width="150"
        />
        <el-table-column
            label="物料名称"
            prop="materialName"
            min-width="150"
        />
        <el-table-column
            label="材质"
            prop="materialSpecification"
            min-width="150"
        />
        <el-table-column
            label="品牌"
            prop="brandName"
            min-width="150"
        />
        <el-table-column
            label="规格型号"
            prop="specNo"
            width="150"
        />
        <el-table-column
            label="单位"
            prop="unit"
            width="150"
        />
        <el-table-column
            label="需求数量"
            prop="requiredQuantity"
            width="100"
        />
        <el-table-column
            label="可供数量"
            prop="availableQuantity"
            min-width="200"
            align="center"
            fixed="right"
        >
          <template #default="{ row }">
            <el-input
                style="width: 100%"
                placeholder="请输入"
                v-model="row.availableQuantity"
                type="number"
            />
          </template>
        </el-table-column>
        <el-table-column
            label="单价（元）"
            prop="unitPrice"
            min-width="200"
            align="center"
            fixed="right"
        >
          <template #default="{ row }">
            <el-input
                style="width: 100%"
                placeholder="请输入"
                v-model="row.unitPrice"
                type="number"
            />
          </template>
        </el-table-column>
        <el-table-column
            label="小计（元）"
            prop="subtotal"
            min-width="200"
            align="center"
            fixed="right"
        >
          <template #default="{ row }">
            <el-input
                style="width: 100%"
                placeholder="请输入"
                v-model="row.subtotal"
                type="number"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 表格下方上传区域 -->
      <div class="flex my-6">
        <div>
          <div class="upload-item">
            <span class="upload-label">报价单：</span>
            <yun-upload
                v-model="quotationFiles"
                :limit="1"
                :file-types="['xlsx', 'xls', 'pdf']"
                :file-size="10"
                type="simple"
            >
              <el-button border type="primary">点击上传</el-button>
            </yun-upload>
          </div>
          <div class="ml-[132px] mt-2 upload-tip">
            jpg/png files with a size less than 500KB.
          </div>
        </div>
        <div>
          <div class="upload-item">
            <span class="upload-label">其他附件：</span>
            <yun-upload
                v-model="otherFiles"
                :limit="5"
                :file-types="['pdf', 'doc', 'docx', 'xlsx', 'xls', 'jpg', 'jpeg', 'png']"
                :file-size="10"
                type="simple"
            >
              <el-button border type="primary">点击上传</el-button>
            </yun-upload>
          </div>
          <div class="ml-[132px] mt-2 upload-tip">
            jpg/png files with a size less than 500KB.
          </div>
        </div>
      </div>
    </div>
  </yun-drawer>
</template>

<script setup lang="ts">
import {ref, reactive, defineProps, defineEmits, watch, computed} from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 组件属性
const props = defineProps<{ visible: boolean }>();
const emit = defineEmits(['update:visible', 'submit']);

const visible = ref(props.visible);
const materialName = ref('');
const materialCode = ref('');
const historyData = ref<any[]>([{}]);

// 筛选相关
const materialNameFilter = ref('');

// 上传文件相关
const quotationFiles = ref<any[]>([]);
const otherFiles = ref<any[]>([]);

// 筛选后的数据
const filteredHistoryData = computed(() => {
  if (!materialNameFilter.value) {
    return historyData.value;
  }
  return historyData.value.filter(item =>
    item.materialName && item.materialName.includes(materialNameFilter.value)
  );
});

watch(
    () => props.visible,
    (v) => (visible.value = v)
);
watch(visible, (v) => emit('update:visible', v));

// 格式化价格
function formatPrice(price: number): string {
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 筛选处理
function handleFilterChange() {
  // 筛选逻辑已在computed中处理
}

// 清空报价
function handleClearQuotation() {
  ElMessageBox.confirm('确认清空所有报价数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 清空报价数据
    historyData.value.forEach(item => {
      item.availableQuantity = '';
      item.unitPrice = '';
      item.subtotal = '';
    });
    ElMessage.success('报价数据已清空');
  }).catch(() => {
    // 取消操作
  });
}

// 导出报价清单
function handleExportQuotation() {
  // 这里可以实现导出逻辑
  ElMessage.info('导出功能开发中...');
}

// 上传报价清单
function handleUploadQuotation() {
  // 这里可以实现上传逻辑
  ElMessage.info('上传功能开发中...');
}

// 处理关闭
function handleClose(done: () => void) {
  done();
}

// 显示抽屉
function show(row: any) {
  visible.value = true;
  materialName.value = row.materialName;
  materialCode.value = row.bidItemCode;

  // 模拟历史报价数据
  historyData.value = [
    {
      id: '1',
      materialCode: 'MT001',
      materialName: '钢筋混凝土',
      materialSpecification: 'HRB400',
      brandName: '宝钢',
      specNo: 'Φ12',
      unit: '吨',
      requiredQuantity: 100,
      availableQuantity: '',
      unitPrice: '',
      subtotal: '',
    },
    {
      id: '2',
      materialCode: 'MT002',
      materialName: '水泥',
      materialSpecification: 'P.O 42.5',
      brandName: '海螺',
      specNo: '50kg/袋',
      unit: '吨',
      requiredQuantity: 50,
      availableQuantity: '',
      unitPrice: '',
      subtotal: '',
    },
    {
      id: '3',
      materialCode: 'MT003',
      materialName: '砂石',
      materialSpecification: '中砂',
      brandName: '本地',
      specNo: '5-10mm',
      unit: '立方米',
      requiredQuantity: 200,
      availableQuantity: '',
      unitPrice: '',
      subtotal: '',
    },
  ];
}

// 暴露方法
defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 0;
}

.price-text {
  font-weight: 500;
  color: var(--Color-Primary-color-primary, #0069ff);
}

// 筛选区域样式
.filter-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.filter-form {
  margin: 0;

  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 上传区域样式
.upload-section {
  margin-top: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.upload-item {
  display: flex;
  flex: 1;
  align-items: center;

  &:last-child {
    margin-bottom: 0;
  }

  .upload-label {
    width: 120px;
    margin-right: 12px;
    text-align: right;
    color: #4E5969;
  }
}

.upload-tip {
  color: var(--Color-Text-text-color-secondary, #86909C);
  /* regular/extra-small */
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}
</style>
