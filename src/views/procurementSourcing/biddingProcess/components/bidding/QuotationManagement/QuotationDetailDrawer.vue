<template>
	<el-drawer
		v-model="visible"
		:title="'报价明细'"
		size="60vw"
		:with-header="true"
		custom-class="custom-drawer"
	>
		<div class="quotation-detail">
			<!-- 报价轮次和基本信息 -->
			<div class="tab-section">
				<el-tabs v-model="activeTab">
					<el-tab-pane
						v-for="item in quotationRounds"
						:key="item.value"
						:label="item.label"
						:name="item.value"
					/>
				</el-tabs>
			</div>

			<!-- 报价基本信息 -->
			<div class="info-section">
				<!-- <div class="info-row">
					<div class="info-item">
						<span class="label">报价时间：</span>
						<span class="value">2025/01/01 12:22</span>
					</div>
					<div class="info-item">
						<span class="label">报价IP：</span>
						<span class="value">************</span>
					</div>
				</div>
				<div class="info-row">
					<div class="info-item">
						<span class="label">报价单：</span>
						<span class="value">
							报价单文件.docx
							<el-link type="primary" class="download-link">下载</el-link>
						</span>
					</div>
					<div class="info-item">
						<span class="label">其他附件：</span>
						<span class="value">
							报价单文件.docx
							<el-link type="primary" class="download-link">下载</el-link>
						</span>
					</div>
				</div> -->

				<div class="left-section">
					<div class="title">第一次报价</div>

					<div class="info-item">
						<span class="info-label">报价时间：</span>
						<span class="info-value">2025/01/01 12:22</span>
					</div>

					<div class="info-item">
						<span class="info-label">报价IP：</span>
						<span class="info-value">************</span>
					</div>
				</div>
				<div class="right-section">
					<div class="info-item">
						<span class="info-label">报价单：</span>
						<span class="info-value">
							报价单文件.docx
							<el-link
								type="primary"
								class="download-link"
								>下载</el-link
							>
						</span>
					</div>

					<div class="info-item">
						<span class="info-label">其他附件：</span>
						<span class="info-value">
							报价单文件.docx
							<el-link
								type="primary"
								class="download-link"
								>下载</el-link
							>
						</span>
					</div>
				</div>
			</div>

			<!-- 搜索区域 -->
			<el-form
				:model="searchForm"
				inline
				class="search-form"
			>
				<div class="form-content">
					<div class="form-item-wrapper">
						<label class="form-label">物料名称</label>
						<el-input
							v-model="searchForm.materialName"
							placeholder="请输入物料名称"
							clearable
							class="search-input"
						/>
					</div>
					<div class="form-item-wrapper">
						<label class="form-label">物料编码</label>
						<el-input
							v-model="searchForm.materialCode"
							placeholder="请输入物料编码"
							clearable
							class="search-input"
						/>
					</div>
				</div>
			</el-form>

			<!-- 报价明细表格 -->
			<el-table
				:data="tableData"
				class="detail-table"
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
				/>
				<el-table-column
					label="物料编码"
					prop="materialCode"
				/>
				<el-table-column
					label="物料名称"
					prop="materialName"
				/>
				<el-table-column
					label="规格型号"
					prop="specNo"
				/>
				<el-table-column
					label="单位"
					prop="unit"
				/>
				<el-table-column
					label="需求数量"
					prop="requiredQuantity"
				/>
				<el-table-column
					label="可供数量"
					prop="availableQuantity"
				/>
				<el-table-column
					label="出厂价"
					prop="factoryPrice"
				>
					<template #default="{ row }"> ¥{{ row.factoryPrice }} </template>
				</el-table-column>
				<el-table-column
					label="运费"
					prop="freight"
				>
					<template #default="{ row }"> ¥{{ row.freight }} </template>
				</el-table-column>
				<el-table-column
					label="到厂价"
					prop="deliveredPrice"
				>
					<template #default="{ row }"> ¥{{ row.deliveredPrice }} </template>
				</el-table-column>
				<el-table-column
					label="报价单价"
					prop="quotationPrice"
				>
					<template #default="{ row }"> ¥{{ row.quotationPrice }} </template>
				</el-table-column>
			</el-table>
		</div>
	</el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';

const props = defineProps<{ visible: boolean }>();
const emit = defineEmits(['update:visible']);

const visible = ref(props.visible);
const activeTab = ref('1');

// 报价轮次
const quotationRounds = [
	{ label: '第一次报价', value: '1' },
	{ label: '第二次报价', value: '2' },
	{ label: '第三次报价', value: '3' },
];

// 搜索表单
interface SearchForm {
	materialName: string;
	materialCode: string;
}

const searchForm = ref<SearchForm>({
	materialName: '',
	materialCode: '',
});

// 表格数据
interface TableRow {
	materialCode: string;
	materialName: string;
	specNo: string;
	unit: string;
	requiredQuantity: number;
	availableQuantity: number;
	factoryPrice: number;
	freight: number;
	deliveredPrice: number;
	quotationPrice: number;
}

const tableData = ref<TableRow[]>([
	{
		materialCode: '2131232132',
		materialName: '草料',
		specNo: '/',
		unit: '吨',
		requiredQuantity: 1000,
		availableQuantity: 1000,
		factoryPrice: 234.22,
		freight: 234.22,
		deliveredPrice: 234.22,
		quotationPrice: 234.22,
	},
]);

watch(
	() => props.visible,
	(v) => (visible.value = v)
);
watch(visible, (v) => emit('update:visible', v));
</script>

<style scoped lang="scss">
.quotation-detail {
	padding: 0 20px;

	.info-section {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;

		.left-section,
		.right-section {
			display: flex;
			align-items: center;
			gap: 8px;
			.title {
				color: var(--Color-Text-text-color-primary, #1d2129);
				font-family: 'PingFang SC';
				font-size: 16px;
				font-style: normal;
				font-weight: 600;
				line-height: 24px;
				position: relative;
				padding-left: 10px;

				&::before {
					content: '';
					display: block;
					width: 2px;
					height: 14px;
					background-color: #0069ff;
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}

		.info-item {
			display: flex;
			align-items: center;
			text-align: center;
			font-family: 'PingFang SC';
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			border-radius: var(--Radius-border-radius-small, 2px);
			border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
			padding: 0 4px;
			.info-label {
				color: var(--Color-Text-text-color-secondary, #86909c);
			}

			.value {
				color: var(--Color-Text-text-color-primary, #1d2129);
				display: flex;
				align-items: center;
				gap: 2px;
			}
		}
	}
	.download-link {
		color: var(--Color-Primary-color-primary, #0069ff);
		font-family: 'PingFang SC';
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
	}

	.search-form {
		margin-bottom: 16px;
		width: 100%;

		.form-content {
			display: flex;
			justify-content: space-between;
			width: 75%;
			gap: 40px;

			.form-item-wrapper {
				flex: 1;
				display: flex;
				align-items: center;
				gap: 8px;

				.form-label {
					width: 70px;
					color: var(--Color-Text-text-color-regular, #4e5969);
					font-family: 'PingFang SC';
					font-size: 14px;
					font-style: normal;
					font-weight: 400;
					line-height: 22px;
					text-align: right;
				}

				.search-input {
					width: 100%;

					:deep(.el-input__wrapper) {
						background-color: var(--Color-Fill-fill-color-light, #f5f7fa);
						box-shadow: none;
						border: 1px solid transparent;

						&:hover,
						&:focus {
							border-color: var(--Color-Primary-color-primary, #0069ff);
							background-color: #fff;
						}
					}

					:deep(.el-input__inner) {
						border-radius: var(--Radius-border-radius-small, 2px);
						background: var(--Color-Fill-fill-color-light, #f5f7fa);
						border: none;
						color: #1d2129;
						font-size: 14px;

						&::placeholder {
							color: var(--Color-Text-text-color-regular, #4e5969);
							font-family: 'PingFang SC';
						}
					}
				}
			}
		}
	}

	.detail-table {
		:deep(.el-table__header) {
			th {
				background-color: var(--Color-Fill-fill-color-light, #f5f7fa);
				color: var(--Color-Text-text-color-primary, #1d2129);
				font-weight: 600;
			}
		}
	}
}
</style>
