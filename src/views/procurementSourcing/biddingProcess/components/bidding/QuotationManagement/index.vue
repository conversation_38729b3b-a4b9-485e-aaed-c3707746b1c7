<template>
	<div class="quotation-management">
		<StageTabs
			:tabs="tabs"
			v-model="activeTabIndex"
		/>
		<!-- 第一行：视图切换和报价信息 -->
		<div class="view-control">
			<div class="left-section">
				<el-radio-group
					v-model="viewMode"
					class="view-tabs"
				>
					<el-radio-button
						v-for="option in viewOptions"
						:key="option.value"
						:label="option.value"
					>
						{{ option.label }}
					</el-radio-button>
				</el-radio-group>
				<el-button @click="handleInsertQuotation">
					<el-icon style="margin-right: 6px; color: #000"><Plus /></el-icon>
					插入供应商报价
				</el-button>
			</div>
			<div class="right-section">已发起 <span class="highlight">3</span> 轮报价</div>
		</div>

		<!-- 第二行：查询条件和导出按钮 -->
		<div class="search-section">
			<div class="left-section">
				<el-form
					:model="queryForm"
					inline
					class="search-form"
					label-width="145px"
				>
					<el-form-item label="报价轮次">
						<el-select
							v-model="queryForm.round"
							placeholder="请选择"
							class="search-select"
						>
							<el-option
								v-for="item in quotationRounds"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="物料名称">
						<el-input
							v-model="queryForm.materialName"
							placeholder="请输入物料名称搜索"
							clearable
							class="search-input"
						/>
					</el-form-item>
				</el-form>
			</div>
			<div class="right-section">
				<el-button @click="handleExport">
					导出
					<el-icon style="margin-left: 6px; color: #000"><Download /></el-icon>
				</el-button>
			</div>
		</div>

		<!-- 第三行：提示信息 -->
		<div class="alert-section">
			<img
				src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/alert_icon.svg"
				alt=""
			/>
			<div>提醒：4条物料报价供应商不足3家，请查看</div>
		</div>

		<!-- 第四行：表格 -->
		<el-table
			:data="tableData"
			class="editable-table"
		>
			<el-table-column
				label="序号"
				type="index"
				width="60"
			/>
			<el-table-column
				label="物料名称"
				prop="materialName"
			/>
			<el-table-column
				label="报价供应商数量"
				prop="supplierCount"
			/>
			<el-table-column
				label="物料编码"
				prop="materialCode"
			/>
			<el-table-column
				label="需求数量"
				prop="requiredQuantity"
			/>
			<el-table-column
				label="规格型号"
				prop="specNo"
			/>
			<el-table-column
				label="计划价格(元)"
				prop="plannedPrice"
			/>
			<el-table-column
				label="操作"
				fixed="right"
				width="100"
			>
				<template #default="{ row }">
					<el-button type="text" @click="handleViewDetail(row)">详情</el-button>
<!--					<el-button type="text" @click="handleQuotation(row)">报价</el-button>-->
				</template>
			</el-table-column>
		</el-table>

		<!-- 供应商报价抽屉 -->
		<QuotationDrawer
			v-model:visible="quotationDrawerVisible"
			@submit="handleQuotationSubmit"
		/>

		<!-- 报价抽屉 -->
		<QuotationViewDrawer
			v-model:visible="quotationViewDrawerVisible"
			@submit="handleQuotationSubmit"
		/>

		<!-- 报价详情抽屉 -->
		<QuotationDetailDrawer
			v-model:visible="detailDrawerVisible"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import StageTabs from '../StageTabs.vue';
import QuotationDrawer from './QuotationDrawer.vue';
import QuotationDetailDrawer from './QuotationDetailDrawer.vue';
import QuotationViewDrawer from './QuotationViewDrawer.vue';
import { Plus, Download, Upload } from '@element-plus/icons-vue';

const tabs = [
	{ key: '1', label: '标段01: 物资采购物资采购...' },
	{ key: '2', label: '标段02: 物资采购物资' },
	{ key: '3', label: '标段03: 施工队伍安排' },
	{ key: '4', label: '标段04: 预算编制' },
];
const activeTabIndex = ref(0);

// Tab 切换相关
const viewMode = ref('material');
const viewOptions = [
	{ label: '按物料查看', value: 'material' },
	{ label: '按供应商查看', value: 'supplier' },
];

// 报价轮次选项
const quotationRounds = [
	{ label: '第一轮报价', value: 1 },
	{ label: '第二轮报价', value: 2 },
	{ label: '第三轮报价', value: 3 },
];

// 查询表单
interface QueryForm {
	round: number;
	materialName: string;
}

const queryForm = ref<QueryForm>({
	round: 1,
	materialName: '',
});

// 表格数据
interface TableRow {
	id: number;
	materialName: string;
	supplierCount: number;
	materialCode: string;
	requiredQuantity: number;
	specNo: string;
	plannedPrice: number;
	operation: string;
}

const tableData = ref<TableRow[]>([
	{
		id: 1,
		materialName: '物料名称01',
		supplierCount: 10,
		materialCode: 'WL001',
		requiredQuantity: 1000,
		specNo: '10',
		plannedPrice: 65949.21,
		operation: '详情',
	},
]);

// 抽屉控制
const quotationDrawerVisible = ref(false);
const quotationViewDrawerVisible = ref(false);

// 详情抽屉控制
const detailDrawerVisible = ref(false);

// 处理导出
function handleExport() {
	console.log('导出');
}

// 处理插入供应商报价
function handleInsertQuotation() {
	quotationDrawerVisible.value = true;
}

// 处理供应商报价提交
function handleQuotationSubmit(form: any) {
	console.log('提交供应商报价:', form);
}

// 查看详情
function handleViewDetail(row: TableRow) {
	detailDrawerVisible.value = true;
}

// 处理报价
function handleQuotation(row: TableRow) {
	quotationViewDrawerVisible.value = true;
}
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';

.quotation-management {
	flex: 1;
	padding: 20px;
	.view-control {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 16px 0;

		.left-section {
			display: flex;
			align-items: center;
			gap: 40px;

			.view-tabs {
				:deep(.el-radio-button__inner) {
					padding: 8px 16px;
				}
			}
		}

		.right-section {
			color: var(--Color-Text-text-color-secondary, #86909c);
			font-family: 'PingFang SC';
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 20px;
			.highlight {
				color: var(--Color-Text-text-color-primary, #1d2129);
				font-family: 'PingFang SC';
				font-size: 12px;
				font-style: normal;
				font-weight: 600;
				line-height: 20px;
			}
		}
	}

	.search-section {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 16px;

		:deep(.el-form .el-form-item) {
			margin-bottom: 0 !important;
		}

		.left-section {
			flex: 1;
		}
	}

	.alert-section {
		display: flex;
		padding: 8px 16px;
		align-items: center;
		gap: 8px;
		align-self: stretch;
		border-radius: var(--Radius-border-radius-small, 2px);
		background: var(--Color-Warning-color-warning-light-9, #fff9e8);
		color: var(--Color-Text-text-color-primary, #1d2129);
		font-family: 'PingFang SC';
		font-size: 13px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
	}
}
</style>
