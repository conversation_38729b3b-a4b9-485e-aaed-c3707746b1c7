<template>
	<el-drawer
		v-model="visible"
		:title="'插入供应商报价'"
		size="60vw"
		:with-header="true"
	>
		<el-form
			:model="form"
			:rules="rules"
			ref="formRef"
			label-width="145px"
		>
			<!-- 供应商信息 -->
			<div class="form-section">
				<el-form-item
					label="供应商名称"
					prop="supplierName"
				>
					<el-select
						v-model="form.supplierName"
						placeholder="请选择供应商"
						clearable
						style="width: 300px !important"
					>
						<el-option
							v-for="item in supplierOptions"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
			</div>

			<!-- 报价清单 -->
			<div class="form-section">
				<div class="label-header">报价清单</div>
				<el-form
					:model="searchForm"
					inline
					class="search-form"
				>
					<div class="form-content">
						<div class="form-item-wrapper">
							<label class="form-label">物料名称</label>
							<el-input
								v-model="searchForm.materialName"
								placeholder="请输入物料名称"
								clearable
								class="search-input"
							/>
						</div>
						<div class="form-item-wrapper">
							<label class="form-label">物料编码</label>
							<el-input
								v-model="searchForm.materialCode"
								placeholder="请输入物料编码"
								clearable
								class="search-input"
							/>
						</div>
						<div class="action-buttons">
							<el-button @click="handleDownloadTemplate">
								<el-icon style="margin-right: 6px"><Download /></el-icon>
								下载报价清单
							</el-button>
							<el-button @click="handleUploadTemplate">
								<el-icon style="margin-right: 6px"><Upload /></el-icon>
								上传报价清单
							</el-button>
						</div>
					</div>
				</el-form>

				<el-table
					:data="form.quotationList"
					style="width: 100%"
					class="editable-table"
				>
					<el-table-column
						label="序号"
						type="index"
						width="60"
					/>
					<el-table-column
						label="物料名称"
						prop="materialName"
					/>
					<el-table-column
						label="物料编码"
						prop="materialCode"
					/>
					<el-table-column
						label="规格型号"
						prop="specNo"
					/>
					<el-table-column
						label="单位"
						prop="unit"
					/>
					<el-table-column
						label="需求数量"
						prop="requiredQuantity"
					/>
					<el-table-column
						label="可供数量"
						prop="availableQuantity"
					>
						<template #default="{ row }">
							<el-input-number
								v-model="row.availableQuantity"
								:min="0"
								:max="row.requiredQuantity"
								:precision="0"
								controls-position="right"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="单价"
						prop="unitPrice"
					>
						<template #default="{ row }">
							<el-input-number
								v-model="row.unitPrice"
								:min="0"
								:precision="2"
								controls-position="right"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="小计"
						prop="subtotal"
					>
						<template #default="{ row }">
							{{ calculateSubtotal(row) }}
						</template>
					</el-table-column>
				</el-table>
			</div>

			<!-- 报价附件 -->
			<div class="form-section">
				<el-form-item
					label="其他附件"
					prop="otherFiles"
				>
					<el-upload
						v-model:file-list="form.otherFiles"
						action="#"
						:limit="5"
						:auto-upload="false"
						list-type="text"
						multiple
					>
						<el-button type="primary">上传文件</el-button>
						<template #tip>
							<div class="el-upload__tip">最多上传5个文件</div>
						</template>
					</el-upload>
				</el-form-item>
			</div>
		</el-form>

		<template #footer>
			<div class="flex justify-end gap-4">
				<el-button @click="visible = false">取消</el-button>
				<el-button
					type="primary"
					@click="handleSubmit"
					:loading="isLoading"
				>
					提交
				</el-button>
			</div>
		</template>
	</el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import { Search, Download, Upload } from '@element-plus/icons-vue';

interface QuotationItem {
	materialName: string;
	materialCode: string;
	specNo: string;
	unit: string;
	requiredQuantity: number;
	availableQuantity: number;
	unitPrice: number;
}

interface QuotationForm {
	supplierName: string;
	quotationList: QuotationItem[];
	quotationFile: any[];
	otherFiles: any[];
}

const props = defineProps<{ visible: boolean }>();
const emit = defineEmits(['update:visible', 'submit']);

const visible = ref(props.visible);
const isLoading = ref(false);
const formRef = ref();

// 供应商选项
const supplierOptions = [
	{ label: '供应商A', value: 'supplierA' },
	{ label: '供应商B', value: 'supplierB' },
	{ label: '供应商C', value: 'supplierC' },
];

// 表单数据
const form = ref<QuotationForm>({
	supplierName: '',
	quotationList: [
		{
			materialName: '草料',
			materialCode: 'WL001',
			specNo: '10',
			unit: '件',
			requiredQuantity: 1000,
			availableQuantity: 1000,
			unitPrice: 234.22,
		},
	],
	quotationFile: [],
	otherFiles: [],
});

// 表单校验规则
const rules = {
	supplierName: [{ required: true, message: '请选择供应商', trigger: 'change' }],
	quotationFile: [{ required: true, message: '请上传报价单', trigger: 'change' }],
};

watch(
	() => props.visible,
	(v) => (visible.value = v)
);
watch(visible, (v) => emit('update:visible', v));

// 计算小计
function calculateSubtotal(row: QuotationItem) {
	return (row.availableQuantity * row.unitPrice).toFixed(2);
}

// 处理下载模板
function handleDownloadTemplate() {
	console.log('下载报价清单模板');
}

// 处理上传模板
function handleUploadTemplate() {
	console.log('上传报价清单');
}

// 处理提交
function handleSubmit() {
	formRef.value?.validate((valid: boolean) => {
		if (valid) {
			isLoading.value = true;
			emit('submit', form.value);
			setTimeout(() => {
				isLoading.value = false;
				visible.value = false;
			}, 1000);
		}
	});
}

// 搜索表单
interface SearchForm {
	materialName: string;
	materialCode: string;
}

const searchForm = ref<SearchForm>({
	materialName: '',
	materialCode: '',
});
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';

.search-form {
	margin-bottom: 16px;
	width: 100%;

	.form-content {
		display: flex;
		justify-content: space-between;
		width: 100%;
		gap: 20px;

		.form-item-wrapper {
			flex: 1;
			display: flex;
			align-items: center;
			gap: 8px;

			.form-label {
				width: 110px;
				color: var(--Color-Text-text-color-regular, #4e5969);
				font-family: 'PingFang SC';
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px;
				text-align: right;
			}

			.search-input {
				width: 100%;
			}

			:deep(.el-input__wrapper),
			:deep(.el-select .el-input__wrapper) {
				background-color: transparent !important;
				border: 1px solid transparent !important;
				box-shadow: none !important;
				padding: 4px 8px;

				&:hover,
				&:focus {
					border-color: var(--Color-Primary-color-primary, #0069ff) !important;
					background-color: #fff !important;
				}
			}

			:deep(.el-input__inner) {
				border-radius: var(--Radius-border-radius-small, 2px) !important;
				background: var(--Color-Fill-fill-color-light, #f5f7fa) !important;
				border: none !important;
				color: #1d2129 !important;
				font-size: 14px;

				&::placeholder {
					color: var(--Color-Text-text-color-regular, #4e5969);
					font-family: 'PingFang SC';
				}
			}
		}

		.action-buttons {
			display: flex;
			gap: 12px;
			align-items: center;
			margin-left: 85px;
		}
	}
}

.editable-table {
	margin-bottom: 20px;
}
</style>
