<template>
	<div class="award-management-container">
		<!-- 步骤指示器 -->
		<StepsIndicator
			:steps="stepsList"
			@step-click="handleStepClick"
		/>

		<!-- 动态组件渲染区域 -->
		<div class="component-content">
			<component :is="currentComponent" />
		</div>

		<!-- 底部操作按钮 -->
		<div class="form-actions-wrapper" v-if="isPurchaser">
			<div class="form-actions">
				<el-button
					type="primary"
					@click="handleSave"
					:loading="saving"
				>
					保存
				</el-button>
				<el-button @click="handleCancel">取消</el-button>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import StepsIndicator, { type StepItem } from '../../StepsIndicator/index.vue';
import AwardResult from '../AwardResult/index.vue';
import WinnerPublicity from '../WinnerPublicity/index.vue';
import WinnerPublicityView from '../WinnerPublicity/view.vue';
import WinnerAnnouncement from '../WinnerAnnouncement/index.vue';
import WinnerAnnouncementView from '../WinnerAnnouncement/view.vue';
import WinnerNotification from '../WinnerNotification/index.vue';
import WinnerNotificationView from '../WinnerNotification/view.vue';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';

const { isPurchaser, isSupplier } = useUserRole();

// 当前激活的步骤索引
const activeStepIndex = ref(10);

// 保存状态
const saving = ref(false);

const stepsList = computed(() => {
	if (isPurchaser.value) {
		return [
			{
				id: 0,
				number: 1,
				label: '定标结果',
				completed: false,
				current: true,
			},
			{
				id: 1,
				number: 2,
				label: '中标公示',
				completed: false,
				current: false,
			},
			{
				id: 2,
				number: 3,
				label: '中标公告',
				completed: false,
				current: false,
			},
			{
				id: 3,
				number: 4,
				label: '中标通知书',
				completed: false,
				current: false,
			},
		];
	} else {
		return [
			{
				id: 10,
				number: 1,
				label: '中标公示',
				completed: false,
				current: true,
			},
			{
				id: 11,
				number: 2,
				label: '中标公告',
				completed: false,
				current: false,
			},
			{
				id: 12,
				number: 3,
				label: '中标通知书',
				completed: false,
				current: false,
			}
		];
	}
});

// 组件映射
const componentMap = {
	0: AwardResult,
	1: WinnerPublicity,
	2: WinnerAnnouncement,
	3: WinnerNotification,
	10: WinnerPublicityView,
	11: WinnerAnnouncementView,
	12: WinnerNotificationView,
};

// 当前显示的组件
const currentComponent = computed(() => {
	return componentMap[activeStepIndex.value as keyof typeof componentMap];
});

// 处理步骤点击事件
function handleStepClick(step: StepItem, index: number) {
	activeStepIndex.value = step.id;
}

// 处理保存
async function handleSave() {
	saving.value = true;
	try {
		// 模拟保存操作
		await new Promise((resolve) => setTimeout(resolve, 1500));

		// 根据当前步骤显示不同的保存成功消息
		const stepNames = ['定标结果', '中标公示', '中标公告', '中标通知书'];
		const currentStepName = stepNames[activeStepIndex.value];

		ElMessage.success(`${currentStepName}保存成功`);

		// 标记当前步骤为已完成
		stepsList.value[activeStepIndex.value].completed = true;
	} catch (error) {
		ElMessage.error('保存失败，请重试');
	} finally {
		saving.value = false;
	}
}

// 处理取消
function handleCancel() {
	ElMessageBox.confirm('确定要取消当前操作吗？未保存的数据将会丢失。', '确认取消', {
		confirmButtonText: '确定',
		cancelButtonText: '继续编辑',
		type: 'warning',
	})
		.then(() => {
			ElMessage.info('已取消操作');
			// 这里可以添加取消后的逻辑，比如返回上一页或重置数据
		})
		.catch(() => {
			// 用户选择继续编辑，不做任何操作
		});
}
</script>

<style lang="scss" scoped>
.award-management-container {
	min-height: 100%;
	display: flex;
	flex-direction: column;

	.component-content {
		flex: 1;
		margin-top: 12px;
		border-radius: 6px;
		//background: var(--Color-Fill-fill-color-blank, #fff);
		margin-bottom: 80px; // 为底部按钮留出空间
	}
}

// 底部操作按钮样式（参考 ProcurementDocument 组件）
.form-actions-wrapper {
	width: 100%;
	box-sizing: border-box;
	background-color: #fff;
	z-index: 1000;
	position: sticky;
	bottom: -20px;
	left: 0;
	right: 0;
	padding: 16px 24px;
	display: flex;
	gap: 12px;
	border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

	.form-actions {
		display: flex;
		justify-content: flex-start;
		gap: 12px;
		width: 100%;
	}
}
</style>
