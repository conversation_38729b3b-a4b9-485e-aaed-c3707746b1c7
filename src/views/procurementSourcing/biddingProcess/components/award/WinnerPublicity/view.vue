<template>
	<div class="winner-publicity-container">
		<div class="bg-[#fff] p-5 mb-3">
      <div class="section-header">
        <div class="header-title">中标信息</div>
      </div>
			<!-- 标签栏 -->
			<StageTabs
        class="my-4"
				:tabs="tabs"
				v-model="activeTabIndex"
			/>

			<!-- 中标信息表格 -->
			<el-table
				:data="winnerTableData"
				style="width: 100%"
				border
				class="winner-table mt-4"
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="供应商名称"
					prop="supplierName"
					min-width="200"
				/>
				<el-table-column
					label="联系人"
					prop="contactPerson"
					width="120"
					align="center"
				/>
				<el-table-column
					label="联系方式"
					prop="contactPhone"
					width="140"
					align="center"
				/>
				<el-table-column
					label="成交价格"
					prop="totalPrice"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span>{{ formatPrice(row.totalPrice) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="操作"
					width="140"
					align="center"
				>
					<template #default="{ row }">
						<el-button
							type="text"
							@click="handleViewDetail(row)"
						>
							查看中标明细
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 公示要求 -->
		<div class="publicity-requirements-section">
			<div class="section-header">
				<div class="header-title">中标公示</div>
			</div>
      <div class="publicity-content">
        <div v-html="publicityForm.content"></div>
      </div>

      <div class="flex mt-5">
        <div>附件：</div>
        <div class="flex items-center gap-2 text-[#1d2129]">
          <span>123456.pdf</span>
          <el-icon size="12" class="color-primary" @click="handleDownload"><download /></el-icon>
        </div>
      </div>

      <div class="flex mt-3">
        <div>发送媒体：</div>
        <div class="text-[#1d2129]">优质来云采购平台（https:www.youzhicai.com)</div>
      </div>
		</div>
	</div>

	<!-- 公告内容编辑抽屉 -->
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';

const publicityForm = reactive({
  content: `<h2 style="font-size: 32px;">XDMY-LYMG-20240810李原牧歌牧场原料采购项目定标公告</h2>
<p>根据《中华人民共和国政府采购法》等有关法律法规的规定，现将本项目的定标结果公告如下：</p>

<h3>一、项目基本信息</h3>
<p><strong>项目名称：</strong>XDMY-LYMG-20240810李原牧歌牧场原料采购项目</p>
<p><strong>项目编号：</strong>XDMY-LYMG-20240810</p>
<p><strong>采购方式：</strong>公开询价</p>
<p><strong>预算金额：</strong>¥500,000.00</p>

<h3>二、定标结果</h3>
<p>经过公开、公平、公正的评标程序，根据评标委员会的评审意见，现确定以下定标结果：</p>
<p><strong>中标供应商：</strong>上海中华控股股份有限公司</p>
<p><strong>中标金额：</strong>¥471,000.00</p>
<p><strong>节约资金：</strong>¥29,000.00</p>

<h3>三、异议期</h3>
<p>供应商对定标结果有异议的，可在公告期限届满之日起7个工作日内，以书面形式向采购人提出质疑。</p>
<p><strong>异议期：</strong>2025-02-01 至 2025-02-08</p>

<h3>四、联系方式</h3>
<p><strong>采购人：</strong>李原牧歌牧场</p>
<p><strong>联系人：</strong>王小二</p>
<p><strong>联系电话：</strong>010-12345678</p>

<p style="text-align: right; margin-top: 30px;">
<strong>采购人：李原牧歌牧场</strong><br>
<strong>日期：2025年1月31日</strong>
</p>`
});

const tabs = [
	{ key: '1', label: '标段01: 物资采购物资采购...' },
	{ key: '2', label: '标段02: 物资采购物资' },
	{ key: '3', label: '标段03: 施工队伍安排' },
	{ key: '4', label: '标段04: 预算编制' },
	{ key: '5', label: '标段05: 进度计划制定' },
	{ key: '6', label: '标段06: 质量控制方案' },
	{ key: '7', label: '标段07: 质量控制方案' },
	{ key: '8', label: '标段08' },
];

const activeTabIndex = ref(0);

// 中标信息表格数据
const winnerTableData = ref([
	{
		id: '1',
		supplierName: '成都运动技科技有限公司',
		contactPerson: '张三',
		contactPhone: '183 2222 4444',
		totalPrice: 2000.00,
	}
]);

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 处理查看中标明细
function handleViewDetail(row: any) {
	ElMessage.info(`查看 ${row.supplierName} 的中标明细功能开发中`);
}

const handleDownload = () => {
  ElMessage.info('下载功能开发中');
}
</script>

<style lang="scss" scoped>
.winner-publicity-container {
  color: #4E5969;
  font-size: 14px;
}

.page-header {

  margin-bottom: 16px;

  .company-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .company-icon {
      color: #0069ff;
      font-size: 18px;
    }

    .company-name {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
    }

    .status-tag {
      margin-left: 8px;
    }
  }
}

.winner-table-section,
.publicity-requirements-section,
.publicity-details-section,
.publicity-attachments-section,
.publicity-platform-section {
  margin-bottom: 12px;
  background: #fff;
  border-radius: 6px;
  padding: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
}

.publicity-form {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-form-item__label) {
    //color: #86909c;
    //font-weight: 400;
  }
}

.winner-table {
  :deep(.el-table__header) {
    th {
      background: #f5f7fa;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      .cell {
        color: #505762;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      td {
        border-bottom: 1px solid #ebeef5;
        padding: 12px 0;
      }
    }
  }
}

.platform-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.el-range-input) {
  background: #fff;
}

.publicity-content {
  display: flex;
  padding: 32px 40px;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  background: var(--Color-Fill-fill-color-light, #F5F7FA);
}

</style>
