import { ref, computed, markRaw, type Component } from 'vue';
import ProjectInfo from '../components/procurement/ProjectInfo/index.vue';
import AnnouncementInfo from '../components/announcement/AnnouncementInfo/index.vue';
import BiddingManagement from '../components/bidding/BiddingManagement/index.vue';
import BidOpeningMangement from '../components/BidOpening/BidOpeningMangement/index.vue';
import AwardManagement from '../components/award/AwardManagement/index.vue';

// 角色类型
export type UserRole = 'purchaser' | 'supplier' | 'guest';
// 招标类型
export type TenderType = 'public' | 'invite';

// 流程节点 key
export enum PROCESS_NODES {
  PROCUREMENT = 'procurement',
  ANNOUNCEMENT = 'announcement',
  BIDDING = 'bidding',
  OPENING = 'opening',
  AWARD = 'award',
}

// 节点配置类型
export interface ProcessNodeConfig {
  key: PROCESS_NODES;
  label: string;
  icon: string;
  component: Component;
}

// 流程状态类型（可扩展）
export interface ProcessFlowState {
  id: string;
  projectId: string;
  currentNode: PROCESS_NODES;
  status: string;
  startTime: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 节点配置表（供应商与采购方保持一致）
const PURCHASER_NODES: ProcessNodeConfig[] = [
  { key: PROCESS_NODES.PROCUREMENT, label: '采购信息', icon: 'Folder', component: ProjectInfo },
  { key: PROCESS_NODES.ANNOUNCEMENT, label: '公告信息', icon: 'Bell', component: AnnouncementInfo },
  { key: PROCESS_NODES.BIDDING, label: '投标管理', icon: 'Document', component: BiddingManagement },
  { key: PROCESS_NODES.OPENING, label: '开标管理', icon: 'Unlock', component: BidOpeningMangement },
  { key: PROCESS_NODES.AWARD, label: '中标管理', icon: 'Trophy', component: AwardManagement },
];

const NODE_CONFIG_MAP: Record<UserRole, Record<TenderType, ProcessNodeConfig[]>> = {
  purchaser: {
    public: PURCHASER_NODES,
    invite: PURCHASER_NODES,
  },
  supplier: {
    public: PURCHASER_NODES,
    invite: PURCHASER_NODES,
  },
  guest: {
    public: [],
    invite: [],
  },
};

interface UseBiddingProcessFlowOptions {
  userRole: UserRole;
  tenderType: TenderType;
  projectId?: string;
  initialNodeIndex?: number;
}

export function useBiddingProcessFlow(options: UseBiddingProcessFlowOptions) {
  const { userRole, tenderType, projectId = 'default-project', initialNodeIndex = 0 } = options;

  // 节点配置
  const processNodes = computed(() => NODE_CONFIG_MAP[userRole][tenderType]);

  // 当前节点索引
  const currentNodeIndex = ref(initialNodeIndex);

  // 当前流程状态
  const currentProcess = ref<ProcessFlowState>({
    id: 'default-process',
    projectId,
    currentNode: processNodes.value[initialNodeIndex]?.key ?? PROCESS_NODES.PROCUREMENT,
    status: 'bidding',
    startTime: new Date().toISOString(),
    createdBy: 'system',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  });

  // 当前节点组件
  const currentComponent = computed(() => {
    const nodes = processNodes.value;
    if (!nodes.length) return null;
    return markRaw(nodes[currentNodeIndex.value]?.component || null);
  });

  // 设置当前节点索引
  function setCurrentNode(index: number) {
    if (index >= 0 && index < processNodes.value.length) {
      currentNodeIndex.value = index;
      currentProcess.value.currentNode = processNodes.value[index].key;
      currentProcess.value.updatedAt = new Date().toISOString();
    }
  }

  // 流程推进
  function moveToNextNode() {
    if (currentNodeIndex.value < processNodes.value.length - 1) {
      setCurrentNode(currentNodeIndex.value + 1);
    }
  }

  // 流程回退
  function moveToPrevNode() {
    if (currentNodeIndex.value > 0) {
      setCurrentNode(currentNodeIndex.value - 1);
    }
  }

  return {
    processNodes,
    currentComponent,
    currentNodeIndex,
    setCurrentNode,
    moveToNextNode,
    moveToPrevNode,
    currentProcess,
  };
} 