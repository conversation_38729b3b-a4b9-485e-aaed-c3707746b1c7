import { ref, computed } from 'vue';

// 用户角色类型
export interface UserRoleInfo {
  userId: string;
  userName: string;
  role: 'purchaser' | 'supplier' | 'guest';
}

// mock 获取当前用户信息
function getCurrentUserMock(): UserRoleInfo {
  // TODO: 替换为真实接口
  return {
    userId: '10001',
    userName: '测试采购员',
    role: 'supplier', // mock: 可改为 'supplier' 或 'guest' 测试
  };
}

// hook: 获取用户身份及是否采购方
export function useUserRole() {
  const userInfo = ref<UserRoleInfo>(getCurrentUserMock());

  // 是否采购方
  const isPurchaser = computed(() => userInfo.value.role === 'purchaser');
  // 是否供应商
  const isSupplier = computed(() => userInfo.value.role === 'supplier');

  return {
    userInfo,
    isPurchaser,
    isSupplier,
  };
}
