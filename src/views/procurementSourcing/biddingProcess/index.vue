<template>
	<div class="bidding-process-container">
		<!-- 项目标题栏 -->
		<div class="project-title-header">
			<div class="title-left">
				<el-button
					type="text"
					size="small"
					icon="ArrowLeft"
				>
					返回
				</el-button>
				<span class="project-title">XDMY-LYMG-20240810李原收敛收场原料采购项目</span>

				<div class="bigging-label">公开询价</div>
			</div>

			<div class="title-right">
				<el-dropdown
					@command="handleDropdownCommand"
					placement="bottom-end"
					trigger="hover"
				>
					<span class="dropdown-trigger">
						<span class="dropdown-text">采购异常</span>
						<el-icon class="dropdown-icon">
							<ArrowDown />
						</el-icon>
					</span>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="reopen">
								<div class="dropdown-item-content">
									<span class="status-dot normal"></span>
									<span>重新开标</span>
								</div>
							</el-dropdown-item>
							<el-dropdown-item command="abandon">
								<div class="dropdown-item-content">
									<span class="status-dot warning"></span>
									<span>流标</span>
								</div>
							</el-dropdown-item>
							<el-dropdown-item command="negotiate">
								<div class="dropdown-item-content">
									<span class="status-dot exception"></span>
									<span>转竞谈</span>
								</div>
							</el-dropdown-item>
							<el-dropdown-item command="single-source">
								<div class="dropdown-item-content">
									<span class="status-dot urgent"></span>
									<span>转单一来源</span>
								</div>
							</el-dropdown-item>
							<el-dropdown-item command="tender">
								<div class="dropdown-item-content">
									<span class="status-dot urgent"></span>
									<span>转招标</span>
								</div>
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>
		</div>

		<div class="process-navigation">
			<!-- 自定义标签页导航 -->
			<div class="custom-tab-navigation">
				<div class="tab-nodes">
					<div
						v-for="(node, index) in processNodes"
						:key="node.key"
						class="tab-node"
						:class="{
							active: currentNodeIndex === index,
							completed: currentNodeIndex > index,
						}"
						@click="handleTabChange(index)"
					>
						<span class="node-dot"></span>
						<span class="node-label">{{ node.label }}</span>
					</div>
				</div>
			</div>

			<div class="additional">
				<el-button
					type="text"
					size="small"
				>
					在线答疑
					<el-icon class="ml-1">
						<ArrowRight />
					</el-icon>
				</el-button>
				<el-button
					type="text"
					size="small"
				>
					文件澄清
					<el-icon class="ml-1">
						<ArrowRight />
					</el-icon>
				</el-button>
			</div>
		</div>

		<!-- 当前节点内容 -->
		<div class="process-content">
			<component
				:is="currentComponent"
				v-if="currentComponent"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useUserRole } from './utils/useUserRole';
import { useBiddingProcessFlow } from './hooks/useBiddingProcessFlow';
import { ArrowRight, ArrowDown } from '@element-plus/icons-vue';

// 获取当前用户角色（mock）
const { userInfo } = useUserRole();
// mock 招标类型，后续可从 props、store、接口等获取
const tenderType = ref<'public' | 'invite'>('public');

// 使用整合后的流程 hook
const {
	processNodes,
	currentComponent,
	currentNodeIndex,
	setCurrentNode,
	moveToNextNode,
	moveToPrevNode,
	currentProcess,
} = useBiddingProcessFlow({
	userRole: userInfo.value.role,
	tenderType: tenderType.value,
	projectId: 'mock-project-id',
	initialNodeIndex: 0,
});

// 切换节点事件
function handleTabChange(index: number) {
	setCurrentNode(index);
}

// 处理下拉框命令
function handleDropdownCommand(command: string) {
	console.log('选择了选项:', command);
	// 根据不同的命令执行相应的操作
	switch (command) {
		case 'reopen':
			console.log('执行重新开标操作');
			break;
		case 'abandon':
			console.log('执行流标操作');
			break;
		case 'negotiate':
			console.log('执行转竞谈操作');
			break;
		case 'single-source':
			console.log('执行转单一来源操作');
			break;
		case 'tender':
			console.log('执行转招标操作');
			break;
		default:
			console.log('未知命令:', command);
	}
}
</script>

<style lang="scss" scoped>
.bidding-process-container {
	position: relative;
	padding: 20px;
	background-color: #f9fafb;
	height: calc(100vh - 84px);
	background: #f0f2f5;
	overflow-y: auto;
	display: flex;
	flex-direction: column;
}

// 项目标题栏样式
.project-title-header {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.title-left {
		display: flex;
		align-items: center;
		gap: 20px;

		.project-title {
			color: var(--Color-Text-text-color-primary, #1d2129);
			font-family: 'PingFang SC';
			font-size: 18px;
			font-style: normal;
			font-weight: 600;
			line-height: 22px;
		}

		.bigging-label {
			display: flex;
			padding: 0px 8px;
			justify-content: center;
			align-items: center;
			gap: 4px;
			border-radius: var(--Radius-border-radius-small, 2px);
			background: var(--Color-Primary-color-primary, #0069ff);
			color: var(--color-white, #fff);
			text-align: center;
			font-family: 'PingFang SC';
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 20px;
		}
	}

	.title-right {
		display: flex;
		align-items: center;
		gap: 20px;

		.dropdown-trigger {
			display: flex;
			align-items: center;
			gap: 4px;
			cursor: pointer;

			.dropdown-text {
				color: var(--Color-Error-color-error, #ff3b30);
				text-align: center;
				/* medium/base */
				font-family: 'PingFang SC';
				font-size: 14px;
				font-style: normal;
				font-weight: 500;
				line-height: 22px;
			}

			.dropdown-icon {
				color: var(--Color-Error-color-error, #ff3b30);
			}
		}

		.dropdown-item-content {
			display: flex;
			align-items: center;
			gap: 8px;

			.status-dot {
				width: 8px;
				height: 8px;
				border-radius: 50%;
				flex-shrink: 0;

				&.normal {
					background-color: #00b42a;
				}

				&.warning {
					background-color: #ffd700;
				}

				&.exception {
					background-color: #ff0000;
				}

				&.urgent {
					background-color: #ff0000;
				}
			}

			span {
				font-size: 14px;
				font-weight: 500;
				color: var(--Color-Text-text-color-primary, #1d2129);
			}
		}
	}
}

.process-navigation {
	margin: 20px 0;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.additional {
		display: flex;
		gap: 16px;

		.el-button {
			display: flex;
			align-items: center;

			.ml-1 {
				margin-left: 4px;
			}
		}
	}
	// 自定义标签页导航
	.custom-tab-navigation {
		display: flex;
		align-items: center;

		.tab-title {
			font-size: 18px;
			font-weight: 600;
			color: var(--Color-Text-text-color-primary, #1d2129);
			margin-right: 40px;
		}

		.tab-nodes {
			display: flex;
			gap: 16px;

			.tab-node {
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				width: 100px;
				height: 40px;
				transition: all 0.2s ease;
				background: url('./asset/imgs/unstart-step.png') no-repeat center center;
				background-size: contain;
				color: var(--Color-Text-text-color-regular, #4e5969);
				text-align: center;
				font-family: 'PingFang SC';
				font-size: 16px;
				font-style: normal;
				font-weight: 400;
				line-height: 24px;

				&.active {
					background: url('./asset/imgs/processing-step.svg') no-repeat center center;
					background-size: contain;
					color: var(--Color-Text-text-color-primary, #1d2129);
					text-align: center;
					font-family: 'PingFang SC';
					font-size: 16px;
					font-style: normal;
					font-weight: 500;
					line-height: 24px;
					.node-dot {
						background-color: #0069ff;
					}
				}

				&.completed {
					background: url('./asset/imgs/complete-step.png') no-repeat center center;
					background-size: contain;
					color: var(--Color-Text-text-color-regular, #4e5969);
					text-align: center;
					font-family: 'PingFang SC';
					font-size: 16px;
					font-style: normal;
					font-weight: 400;
					line-height: 24px;
					.node-dot {
						background-color: #00b42a;
					}
				}

				.node-dot {
					display: inline-block;
					width: 5px;
					height: 5px;
					border-radius: 50%;
					background-color: #86909c;
					margin-right: 4px;
				}

				.node-label {
					font-size: 14px;
					font-weight: 500;
				}
			}
		}
	}
}

.process-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	.content-placeholder {
		text-align: center;
		padding: 2rem;

		.placeholder-title {
			font-size: 1.25rem;
			font-weight: 600;
			color: #1f2937;
			margin-bottom: 1rem;
		}

		.placeholder-description {
			color: #6b7280;
			margin-bottom: 2rem;
			line-height: 1.6;
		}

		.process-controls {
			display: flex;
			justify-content: center;
			gap: 1rem;
			margin-top: 2rem;
			padding-top: 1.5rem;
			border-top: 1px solid #e5e7eb;

			.btn {
				padding: 0.5rem 1rem;
				border-radius: 0.375rem;
				border: none;
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s ease;

				&.btn-primary {
					background-color: #3b82f6;
					color: white;

					&:hover {
						background-color: #2563eb;
					}
				}

				&.btn-secondary {
					background-color: #6b7280;
					color: white;

					&:hover {
						background-color: #4b5563;
					}
				}
			}
		}
	}
}
</style>

<style lang="scss">
@import './styles/index.scss';
</style>
